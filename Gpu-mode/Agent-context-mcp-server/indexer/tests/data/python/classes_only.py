"""
Module containing only classes for testing class extraction.
"""

from abc import ABC, abstractmethod
from typing import Protocol, Generic, TypeVar


T = TypeVar('T')


class BaseClass:
    """A simple base class."""
    
    def __init__(self, name: str):
        """Initialize the base class."""
        self.name = name
    
    def get_name(self) -> str:
        """Get the name."""
        return self.name


class AbstractBaseClass(ABC):
    """An abstract base class."""
    
    @abstractmethod
    def process(self) -> None:
        """Abstract method that must be implemented."""
        pass
    
    def common_method(self) -> str:
        """A common method available to all subclasses."""
        return "Common functionality"


class ConcreteClass(AbstractBaseClass):
    """A concrete implementation of the abstract base class."""
    
    def __init__(self, value: int):
        """Initialize with a value."""
        self.value = value
    
    def process(self) -> None:
        """Implementation of the abstract method."""
        print(f"Processing value: {self.value}")
    
    def get_value(self) -> int:
        """Get the stored value."""
        return self.value


class MultipleInheritanceClass(BaseClass, ConcreteClass):
    """A class that inherits from multiple base classes."""
    
    def __init__(self, name: str, value: int, extra: str = ""):
        """Initialize with multiple parent constructors."""
        BaseClass.__init__(self, name)
        ConcreteClass.__init__(self, value)
        self.extra = extra
    
    def get_info(self) -> dict:
        """Get combined information."""
        return {
            "name": self.name,
            "value": self.value,
            "extra": self.extra
        }


class GenericClass(Generic[T]):
    """A generic class with type parameters."""
    
    def __init__(self, item: T):
        """Initialize with a generic item."""
        self.item = item
    
    def get_item(self) -> T:
        """Get the stored item."""
        return self.item
    
    def set_item(self, item: T) -> None:
        """Set a new item."""
        self.item = item


class ProtocolClass(Protocol):
    """A protocol class defining an interface."""
    
    def required_method(self) -> str:
        """A method that implementing classes must have."""
        ...
    
    @property
    def required_property(self) -> int:
        """A property that implementing classes must have."""
        ...


class DataClass:
    """A class that acts like a data container."""
    
    def __init__(self, x: float, y: float, z: float = 0.0):
        """
        Initialize a 3D point.
        
        Args:
            x: X coordinate
            y: Y coordinate  
            z: Z coordinate (default 0.0)
        """
        self.x = x
        self.y = y
        self.z = z
    
    def __str__(self) -> str:
        """String representation."""
        return f"Point({self.x}, {self.y}, {self.z})"
    
    def __repr__(self) -> str:
        """Developer representation."""
        return f"DataClass(x={self.x}, y={self.y}, z={self.z})"
    
    def __eq__(self, other) -> bool:
        """Equality comparison."""
        if not isinstance(other, DataClass):
            return False
        return self.x == other.x and self.y == other.y and self.z == other.z
    
    def distance_from_origin(self) -> float:
        """Calculate distance from origin."""
        return (self.x ** 2 + self.y ** 2 + self.z ** 2) ** 0.5


class NestedClass:
    """A class containing nested classes."""
    
    class InnerClass:
        """An inner class."""
        
        def __init__(self, value: str):
            """Initialize the inner class."""
            self.value = value
        
        def get_value(self) -> str:
            """Get the inner value."""
            return self.value
    
    def __init__(self, outer_value: int):
        """Initialize the outer class."""
        self.outer_value = outer_value
        self.inner = self.InnerClass(f"inner_{outer_value}")
    
    def get_combined(self) -> str:
        """Get combined values."""
        return f"Outer: {self.outer_value}, Inner: {self.inner.get_value()}"


class PropertyClass:
    """A class demonstrating property usage."""
    
    def __init__(self, initial_value: int = 0):
        """Initialize with an initial value."""
        self._value = initial_value
        self._computed_cache = None
    
    @property
    def value(self) -> int:
        """Get the current value."""
        return self._value
    
    @value.setter
    def value(self, new_value: int) -> None:
        """Set a new value and clear cache."""
        self._value = new_value
        self._computed_cache = None
    
    @property
    def computed_value(self) -> int:
        """Get a computed value (cached)."""
        if self._computed_cache is None:
            self._computed_cache = self._value ** 2 + self._value
        return self._computed_cache
    
    @property
    def read_only_property(self) -> str:
        """A read-only property."""
        return f"Value is {self._value}"
