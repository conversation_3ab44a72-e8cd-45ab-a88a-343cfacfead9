"""
Simple Python module for testing the Python parser.

This module contains basic Python constructs including functions,
classes, imports, and module-level code.
"""

import os
import sys
from typing import List, Dict, Optional
from pathlib import Path


# Module-level constant
DEFAULT_CONFIG = {
    "debug": False,
    "max_retries": 3,
    "timeout": 30
}


def simple_function(name: str) -> str:
    """
    A simple function that greets a person.
    
    Args:
        name: The name of the person to greet
        
    Returns:
        A greeting message
    """
    return f"Hello, {name}!"


def function_with_complex_logic(data: List[Dict[str, any]], threshold: float = 0.5) -> Optional[Dict]:
    """
    Function with more complex logic for testing complexity scoring.
    
    Args:
        data: List of dictionaries to process
        threshold: Minimum threshold for filtering
        
    Returns:
        Processed data or None if no valid data
    """
    if not data:
        return None
    
    filtered_data = []
    for item in data:
        if "score" in item and item["score"] >= threshold:
            processed_item = {
                "id": item.get("id", "unknown"),
                "score": item["score"],
                "category": item.get("category", "default")
            }
            
            # Additional processing based on category
            if processed_item["category"] == "premium":
                processed_item["bonus"] = processed_item["score"] * 0.1
            elif processed_item["category"] == "standard":
                processed_item["bonus"] = processed_item["score"] * 0.05
            else:
                processed_item["bonus"] = 0.0
                
            filtered_data.append(processed_item)
    
    if not filtered_data:
        return None
        
    return {
        "items": filtered_data,
        "count": len(filtered_data),
        "average_score": sum(item["score"] for item in filtered_data) / len(filtered_data)
    }


@property
def decorated_function():
    """Function with a decorator."""
    return "This function has a decorator"


class SimpleClass:
    """A simple class for testing class parsing."""
    
    def __init__(self, name: str, value: int = 0):
        """
        Initialize the class.
        
        Args:
            name: Name of the instance
            value: Initial value
        """
        self.name = name
        self.value = value
    
    def get_name(self) -> str:
        """Get the name."""
        return self.name
    
    def increment(self, amount: int = 1) -> None:
        """Increment the value."""
        self.value += amount


class InheritedClass(SimpleClass):
    """A class that inherits from SimpleClass."""
    
    def __init__(self, name: str, value: int = 0, extra_data: str = ""):
        """
        Initialize the inherited class.
        
        Args:
            name: Name of the instance
            value: Initial value
            extra_data: Additional data
        """
        super().__init__(name, value)
        self.extra_data = extra_data
    
    def get_info(self) -> Dict[str, any]:
        """Get complete information about the instance."""
        return {
            "name": self.name,
            "value": self.value,
            "extra_data": self.extra_data
        }


class ComplexClass:
    """A more complex class with multiple methods and properties."""
    
    def __init__(self, config: Dict[str, any] = None):
        """Initialize with configuration."""
        self.config = config or DEFAULT_CONFIG.copy()
        self._internal_state = {}
    
    @property
    def state(self) -> Dict:
        """Get the internal state."""
        return self._internal_state.copy()
    
    @state.setter
    def state(self, value: Dict) -> None:
        """Set the internal state."""
        if not isinstance(value, dict):
            raise ValueError("State must be a dictionary")
        self._internal_state = value.copy()
    
    def process_data(self, input_data: List[any]) -> List[any]:
        """Process input data according to configuration."""
        if not input_data:
            return []
        
        processed = []
        for item in input_data:
            try:
                if self.config.get("debug", False):
                    print(f"Processing item: {item}")
                
                # Complex processing logic
                if isinstance(item, (int, float)):
                    result = item * 2
                elif isinstance(item, str):
                    result = item.upper()
                elif isinstance(item, dict):
                    result = {k: v for k, v in item.items() if v is not None}
                else:
                    result = str(item)
                
                processed.append(result)
                
            except Exception as e:
                if self.config.get("debug", False):
                    print(f"Error processing item {item}: {e}")
                continue
        
        return processed


# Module-level code
if __name__ == "__main__":
    # Example usage
    simple_obj = SimpleClass("test", 42)
    print(simple_function(simple_obj.get_name()))
    
    # Test complex function
    test_data = [
        {"id": "1", "score": 0.8, "category": "premium"},
        {"id": "2", "score": 0.3, "category": "standard"},
        {"id": "3", "score": 0.9, "category": "premium"}
    ]
    
    result = function_with_complex_logic(test_data, 0.5)
    if result:
        print(f"Processed {result['count']} items with average score {result['average_score']:.2f}")
