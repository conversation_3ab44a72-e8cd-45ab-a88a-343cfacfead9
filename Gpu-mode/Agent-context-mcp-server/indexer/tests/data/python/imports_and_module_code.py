"""
Module for testing import extraction and module-level code parsing.

This file contains various types of imports and module-level code
that should be properly extracted by the parser.
"""

# Standard library imports
import os
import sys
import json
import re
from pathlib import Path
from typing import List, Dict, Optional, Union, Any
from collections import defaultdict, Counter
from datetime import datetime, timedelta

# Third-party imports (simulated)
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from requests import get, post
from flask import Flask, request, jsonify

# Relative imports (simulated)
from .utils import helper_function, CONSTANT_VALUE
from ..config import settings
from ...base import BaseProcessor

# Import with alias
import matplotlib.pyplot as plt
from typing import Dict as DictType

# Import everything (not recommended but used for testing)
from math import *

# Conditional imports
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

# Module-level constants
VERSION = "1.0.0"
DEBUG = True
MAX_RETRIES = 3

# Module-level configuration
CONFIG = {
    "api_url": "https://api.example.com",
    "timeout": 30,
    "max_workers": 4,
    "cache_size": 1000
}

# Module-level data structures
SUPPORTED_FORMATS = ["json", "csv", "xml", "yaml"]
ERROR_CODES = {
    100: "Success",
    200: "Warning", 
    300: "Error",
    400: "Critical Error"
}

# Module-level variables with complex initialization
current_time = datetime.now()
log_file_path = Path(os.environ.get("LOG_DIR", "/tmp")) / f"app_{current_time.strftime('%Y%m%d')}.log"

# Module-level function calls
default_config = CONFIG.copy()
supported_extensions = [f".{fmt}" for fmt in SUPPORTED_FORMATS]

# Complex module-level logic
if DEBUG:
    print(f"Debug mode enabled. Version: {VERSION}")
    print(f"Log file: {log_file_path}")

# Module-level class instantiation
app = Flask(__name__) if 'Flask' in globals() else None

# Module-level conditional logic
if HAS_TORCH:
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"PyTorch available. Using device: {device}")
else:
    device = None
    print("PyTorch not available")

# Module-level exception handling
try:
    with open(log_file_path.parent / "config.json", "r") as f:
        runtime_config = json.load(f)
        CONFIG.update(runtime_config)
except FileNotFoundError:
    print("No runtime config found, using defaults")
except json.JSONDecodeError:
    print("Invalid runtime config, using defaults")

# Module-level data processing
processed_data = []
for code, message in ERROR_CODES.items():
    processed_data.append({
        "code": code,
        "message": message,
        "severity": "high" if code >= 300 else "low"
    })

# Module-level function definition (simple)
def module_level_function():
    """A simple function defined at module level."""
    return "Module level function called"

# Module-level execution guard
if __name__ == "__main__":
    print("Running as main module")
    print(f"Supported formats: {SUPPORTED_FORMATS}")
    print(f"Config: {CONFIG}")
    
    # Test module-level function
    result = module_level_function()
    print(f"Function result: {result}")
    
    # More complex main execution
    for i, item in enumerate(processed_data):
        print(f"{i+1}. {item['code']}: {item['message']} (severity: {item['severity']})")
    
    # Conditional execution based on environment
    if os.environ.get("RUN_TESTS", "false").lower() == "true":
        print("Running tests...")
        # Simulate test execution
        test_results = {"passed": 15, "failed": 2, "skipped": 1}
        print(f"Test results: {test_results}")
    
    print("Module execution completed")

# Module-level cleanup or final setup
atexit_registered = False
try:
    import atexit
    
    def cleanup():
        """Cleanup function to run at exit."""
        print("Performing cleanup...")
    
    atexit.register(cleanup)
    atexit_registered = True
except ImportError:
    pass

# Final module-level statement
print(f"Module {__name__} loaded successfully. Atexit registered: {atexit_registered}")
