"""
Module containing only functions for testing function extraction.
"""

import math
from typing import Union, List


def add_numbers(a: int, b: int) -> int:
    """Add two numbers together."""
    return a + b


def multiply_numbers(x: float, y: float) -> float:
    """Multiply two numbers."""
    return x * y


@staticmethod
def static_function(value: str) -> str:
    """A static function with decorator."""
    return value.strip().lower()


@classmethod
def class_method_function(cls, data: List[int]) -> int:
    """A class method function."""
    return sum(data)


def function_with_nested_logic(numbers: List[Union[int, float]]) -> dict:
    """
    Function with nested logic and multiple return paths.
    
    Args:
        numbers: List of numbers to process
        
    Returns:
        Dictionary with statistics
    """
    if not numbers:
        return {"error": "Empty list provided"}
    
    try:
        # Calculate basic statistics
        total = sum(numbers)
        count = len(numbers)
        average = total / count
        
        # Find min and max
        minimum = min(numbers)
        maximum = max(numbers)
        
        # Calculate standard deviation
        variance = sum((x - average) ** 2 for x in numbers) / count
        std_dev = math.sqrt(variance)
        
        # Categorize numbers
        positive_count = sum(1 for x in numbers if x > 0)
        negative_count = sum(1 for x in numbers if x < 0)
        zero_count = count - positive_count - negative_count
        
        return {
            "total": total,
            "count": count,
            "average": average,
            "minimum": minimum,
            "maximum": maximum,
            "std_deviation": std_dev,
            "positive_count": positive_count,
            "negative_count": negative_count,
            "zero_count": zero_count
        }
        
    except Exception as e:
        return {"error": f"Calculation failed: {str(e)}"}


async def async_function(delay: float = 1.0) -> str:
    """An async function for testing async detection."""
    import asyncio
    await asyncio.sleep(delay)
    return f"Completed after {delay} seconds"


def recursive_function(n: int) -> int:
    """A recursive function to calculate factorial."""
    if n <= 1:
        return 1
    return n * recursive_function(n - 1)


def generator_function(start: int, end: int):
    """A generator function."""
    for i in range(start, end + 1):
        yield i * 2


def function_with_multiple_decorators():
    """Function with multiple decorators (simulated)."""
    pass


# Simulate multiple decorators with comments
# @decorator1
# @decorator2
# @decorator3
def simulated_decorated_function(param: str) -> str:
    """Function that would have multiple decorators."""
    return f"Processed: {param}"
