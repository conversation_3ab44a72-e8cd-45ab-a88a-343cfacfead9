"""
A Python file with no functions or classes.

This file contains only module-level code, imports, and variable definitions.
It's useful for testing how the parser handles files without semantic structures.
"""

import os
import sys
from pathlib import Path

# Simple variable assignments
name = "test_module"
version = "1.0.0"
debug_mode = True

# List and dictionary definitions
supported_languages = ["python", "javascript", "typescript", "markdown"]
configuration = {
    "max_file_size": 1024 * 1024,  # 1MB
    "allowed_extensions": [".py", ".js", ".ts", ".md"],
    "enable_caching": True,
    "cache_ttl": 3600
}

# Complex data structures
file_stats = {
    "total_files": 0,
    "processed_files": 0,
    "failed_files": 0,
    "start_time": None,
    "end_time": None
}

# Mathematical calculations
pi_approximation = 22 / 7
golden_ratio = (1 + 5 ** 0.5) / 2
fibonacci_sequence = [0, 1, 1, 2, 3, 5, 8, 13, 21, 34, 55]

# String processing
welcome_message = "Welcome to the Python parser test module!"
formatted_message = f"Module: {name} v{version}"
multiline_text = """
This is a multiline string that spans
multiple lines and contains various
types of content for testing purposes.

It includes:
- Multiple paragraphs
- Special characters: !@#$%^&*()
- Numbers: 123456789
- Unicode: 🐍 Python is awesome! 🚀
"""

# File path operations
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
config_file = project_root / "config" / "settings.json"

# Environment variable handling
log_level = os.environ.get("LOG_LEVEL", "INFO")
data_directory = Path(os.environ.get("DATA_DIR", "/tmp/data"))

# Conditional logic
if debug_mode:
    print(f"Debug mode is enabled for {name}")
    print(f"Configuration: {configuration}")

# Loop processing
processed_languages = []
for lang in supported_languages:
    processed_lang = {
        "name": lang,
        "extension": f".{lang[:2]}",
        "enabled": True
    }
    processed_languages.append(processed_lang)

# List comprehensions
squared_fibonacci = [x ** 2 for x in fibonacci_sequence]
even_fibonacci = [x for x in fibonacci_sequence if x % 2 == 0]

# Dictionary comprehensions
lang_config = {lang: {"enabled": True, "priority": i} 
               for i, lang in enumerate(supported_languages)}

# Exception handling
try:
    with open(config_file, "r") as f:
        external_config = f.read()
        print("External configuration loaded")
except FileNotFoundError:
    print("No external configuration file found")
    external_config = "{}"

# More complex processing
total_fibonacci = sum(fibonacci_sequence)
average_fibonacci = total_fibonacci / len(fibonacci_sequence)

# String manipulation
reversed_name = name[::-1]
uppercase_languages = [lang.upper() for lang in supported_languages]

# Nested data structures
project_structure = {
    "src": {
        "parsers": ["python.py", "javascript.py", "markdown.py"],
        "tests": ["test_python.py", "test_javascript.py"],
        "utils": ["helpers.py", "constants.py"]
    },
    "docs": {
        "api": ["parser_api.md", "usage.md"],
        "examples": ["basic_usage.py", "advanced_usage.py"]
    },
    "config": {
        "settings.json": None,
        "logging.conf": None
    }
}

# Final calculations and assignments
total_files_in_project = sum(
    len(files) for files in project_structure.values() 
    if isinstance(files, list)
)

summary_stats = {
    "module_name": name,
    "version": version,
    "supported_languages": len(supported_languages),
    "fibonacci_sum": total_fibonacci,
    "fibonacci_average": average_fibonacci,
    "project_files": total_files_in_project,
    "debug_enabled": debug_mode
}

# Print final summary
print(f"Module {name} initialization complete")
print(f"Summary: {summary_stats}")
print(f"Processed {len(processed_languages)} languages")
print(f"Golden ratio approximation: {golden_ratio:.6f}")

# Module-level assertions for validation
assert len(supported_languages) > 0, "Must have at least one supported language"
assert version.count(".") == 2, "Version must be in format X.Y.Z"
assert total_fibonacci > 0, "Fibonacci sum must be positive"

print("All module-level validations passed")
