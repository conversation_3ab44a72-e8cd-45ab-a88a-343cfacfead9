"""
Tests for the Python parser.

These tests fall under the "Parser accuracy for different languages" category
from the testing strategy in INDEXER.md and use small synthetic codebases
for test data.
"""

import sys
from pathlib import Path

# Add the parent directory to the path so we can import from the indexer package
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from parsers.python import PythonParser
from parsers.base import ParseResult


class TestPythonParser:
    """Test suite for PythonParser."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = PythonParser()

    def test_get_language(self):
        """Test that parser returns correct language identifier."""
        assert self.parser.get_language() == "python"

    def test_get_supported_extensions(self):
        """Test that parser returns correct supported extensions."""
        extensions = self.parser.get_supported_extensions()
        expected = [".py", ".pyw", ".pyi"]
        assert extensions == expected

    def test_parse_simple_function(self):
        """Test parsing a simple function."""
        content = '''def hello_world():
    """A simple hello world function."""
    return "Hello, World!"
'''
        file_path = Path("test.py")
        result = self.parser.parse(content, file_path)

        assert isinstance(result, ParseResult)
        assert len(result.chunks) >= 1
        assert result.metadata["language"] == "python"
        assert len(result.errors) == 0

        # Find function chunk
        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        assert len(func_chunks) >= 1

        func_chunk = func_chunks[0]
        assert func_chunk["metadata"]["function_name"] == "hello_world"
        assert func_chunk["metadata"]["docstring"] == "A simple hello world function."
        assert "def hello_world():" in func_chunk["content"]

    def test_parse_function_with_parameters(self):
        """Test parsing function with parameters and type hints."""
        content = '''def greet_person(name: str, age: int = 25) -> str:
    """
    Greet a person with their name and age.
    
    Args:
        name: The person's name
        age: The person's age (default 25)
        
    Returns:
        A greeting message
    """
    return f"Hello {name}, you are {age} years old!"
'''
        file_path = Path("greet.py")
        result = self.parser.parse(content, file_path)

        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        assert len(func_chunks) == 1

        func_chunk = func_chunks[0]
        assert func_chunk["metadata"]["function_name"] == "greet_person"
        assert "Greet a person with their name and age." in func_chunk["metadata"]["docstring"]
        assert func_chunk["metadata"]["start_line"] == 1
        assert func_chunk["metadata"]["end_line"] >= 8

    def test_parse_simple_class(self):
        """Test parsing a simple class."""
        content = '''class Calculator:
    """A simple calculator class."""
    
    def __init__(self, initial_value: int = 0):
        """Initialize the calculator."""
        self.value = initial_value
    
    def add(self, number: int) -> int:
        """Add a number to the current value."""
        self.value += number
        return self.value
'''
        file_path = Path("calculator.py")
        result = self.parser.parse(content, file_path)

        class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]
        assert len(class_chunks) >= 1

        class_chunk = class_chunks[0]
        assert class_chunk["metadata"]["class_name"] == "Calculator"
        assert class_chunk["metadata"]["docstring"] == "A simple calculator class."
        assert "class Calculator:" in class_chunk["content"]

    def test_parse_class_with_inheritance(self):
        """Test parsing class with inheritance."""
        content = '''class Animal:
    """Base animal class."""
    
    def __init__(self, name: str):
        self.name = name

class Dog(Animal):
    """A dog class that inherits from Animal."""
    
    def __init__(self, name: str, breed: str):
        super().__init__(name)
        self.breed = breed
    
    def bark(self) -> str:
        """Make the dog bark."""
        return f"{self.name} says woof!"
'''
        file_path = Path("animals.py")
        result = self.parser.parse(content, file_path)

        class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]
        assert len(class_chunks) >= 2

        # Find the Dog class
        dog_chunk = next(chunk for chunk in class_chunks if chunk["metadata"]["class_name"] == "Dog")
        assert dog_chunk["metadata"]["docstring"] == "A dog class that inherits from Animal."
        
        # Check if base classes are detected (this depends on parser implementation)
        if "base_classes" in dog_chunk["metadata"]:
            assert "Animal" in dog_chunk["metadata"]["base_classes"]

    def test_parse_imports(self):
        """Test parsing import statements."""
        content = '''import os
import sys
from typing import List, Dict
from pathlib import Path

def process_file(file_path: Path) -> Dict:
    """Process a file."""
    return {"status": "processed"}
'''
        file_path = Path("imports.py")
        result = self.parser.parse(content, file_path)

        # Check that imports are extracted
        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        if func_chunks:
            func_chunk = func_chunks[0]
            imports = func_chunk["metadata"]["imports"]
            assert len(imports) >= 3
            
            import_text = " ".join(imports)
            assert "import os" in import_text
            assert "from typing import" in import_text
            assert "from pathlib import Path" in import_text

    def test_parse_decorated_function(self):
        """Test parsing function with decorators."""
        content = '''@property
def decorated_function():
    """A function with a decorator."""
    return "decorated"

@staticmethod
def static_method():
    """A static method."""
    return "static"
'''
        file_path = Path("decorated.py")
        result = self.parser.parse(content, file_path)

        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        assert len(func_chunks) >= 1

        # Check if decorators are detected (depends on parser implementation)
        for chunk in func_chunks:
            if "decorators" in chunk["metadata"]:
                decorators = chunk["metadata"]["decorators"]
                if chunk["metadata"]["function_name"] == "decorated_function":
                    assert any("property" in dec for dec in decorators)

    def test_parse_empty_file(self):
        """Test parsing empty Python file."""
        content = ""
        file_path = Path("empty.py")
        result = self.parser.parse(content, file_path)

        assert isinstance(result, ParseResult)
        assert result.metadata["language"] == "python"
        # Empty file should still produce some result
        assert len(result.chunks) >= 0

    def test_parse_whitespace_only(self):
        """Test parsing file with only whitespace."""
        content = "   \n\n  \t  \n"
        file_path = Path("whitespace.py")
        result = self.parser.parse(content, file_path)

        assert isinstance(result, ParseResult)
        assert result.metadata["language"] == "python"

    def test_parse_syntax_error_handling(self):
        """Test handling of syntax errors in Python code."""
        content = '''def broken_function(
    """This function has a syntax error."""
    return "broken"
'''
        file_path = Path("broken.py")
        result = self.parser.parse(content, file_path)

        # Parser should handle errors gracefully
        assert isinstance(result, ParseResult)
        # May have errors or fall back to simple parsing
        assert len(result.chunks) >= 0

    def test_complexity_calculation(self):
        """Test that complexity scores are calculated."""
        content = '''def complex_function(data):
    """A function with some complexity."""
    if not data:
        return None
    
    result = []
    for item in data:
        if isinstance(item, dict):
            for key, value in item.items():
                if value is not None:
                    processed = {
                        "key": key,
                        "value": value,
                        "type": type(value).__name__
                    }
                    result.append(processed)
    
    return result
'''
        file_path = Path("complex.py")
        result = self.parser.parse(content, file_path)

        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        assert len(func_chunks) >= 1

        func_chunk = func_chunks[0]
        assert "complexity_score" in func_chunk["metadata"]
        complexity = func_chunk["metadata"]["complexity_score"]
        assert isinstance(complexity, (int, float))
        assert 0.0 <= complexity <= 1.0

    def test_line_numbers_accuracy(self):
        """Test that line numbers in chunks are accurate."""
        content = '''# Line 1
def first_function():
    """First function."""
    return "first"

# Line 6
class TestClass:
    """Test class."""
    
    def method(self):
        """A method."""
        return "method"
'''
        file_path = Path("line_numbers.py")
        result = self.parser.parse(content, file_path)

        # Check that line numbers are reasonable
        for chunk in result.chunks:
            start_line = chunk["metadata"]["start_line"]
            end_line = chunk["metadata"]["end_line"]
            
            assert start_line >= 1
            assert end_line >= start_line
            assert end_line <= len(content.split('\n'))

    def test_file_path_metadata(self):
        """Test that file path metadata is correctly set."""
        content = '''def test_function():
    """Test function."""
    pass
'''
        file_path = Path("test_file.py")
        result = self.parser.parse(content, file_path)

        # Check result metadata
        assert result.metadata["file_path"] == str(file_path)

        # Check chunk metadata
        for chunk in result.chunks:
            assert chunk["metadata"]["file_path"] == str(file_path)
            assert chunk["metadata"]["language"] == "python"


class TestPythonParserWithTestData:
    """Test suite using synthetic test data files."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = PythonParser()
        self.test_data_dir = Path(__file__).parent.parent / "data" / "python"

    def test_parse_simple_module(self):
        """Test parsing a simple Python module with functions and classes."""
        module_path = self.test_data_dir / "simple_module.py"
        content = module_path.read_text()

        result = self.parser.parse(content, module_path)

        assert len(result.chunks) >= 3  # Should have multiple functions and classes
        assert len(result.errors) == 0

        # Check for expected function chunks
        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        func_names = [chunk["metadata"]["function_name"] for chunk in func_chunks]

        assert "simple_function" in func_names
        assert "function_with_complex_logic" in func_names

        # Check for expected class chunks
        class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]
        class_names = [chunk["metadata"]["class_name"] for chunk in class_chunks]

        assert "SimpleClass" in class_names
        assert "InheritedClass" in class_names
        assert "ComplexClass" in class_names

        # Verify inheritance is detected for InheritedClass
        inherited_chunk = next(chunk for chunk in class_chunks
                             if chunk["metadata"]["class_name"] == "InheritedClass")
        if "base_classes" in inherited_chunk["metadata"]:
            assert "SimpleClass" in inherited_chunk["metadata"]["base_classes"]

    def test_parse_functions_only(self):
        """Test parsing a file with only functions."""
        functions_path = self.test_data_dir / "functions_only.py"
        content = functions_path.read_text()

        result = self.parser.parse(content, functions_path)

        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        assert len(func_chunks) >= 5  # Should have multiple functions

        func_names = [chunk["metadata"]["function_name"] for chunk in func_chunks]
        assert "add_numbers" in func_names
        assert "multiply_numbers" in func_names
        assert "function_with_nested_logic" in func_names
        assert "async_function" in func_names
        assert "recursive_function" in func_names

        # Check that docstrings are extracted
        add_numbers_chunk = next(chunk for chunk in func_chunks
                               if chunk["metadata"]["function_name"] == "add_numbers")
        assert add_numbers_chunk["metadata"]["docstring"] == "Add two numbers together."

        # Check complexity scores
        nested_logic_chunk = next(chunk for chunk in func_chunks
                                if chunk["metadata"]["function_name"] == "function_with_nested_logic")
        complexity = nested_logic_chunk["metadata"]["complexity_score"]
        assert complexity > 0.1  # Should have higher complexity due to nested logic

    def test_parse_classes_only(self):
        """Test parsing a file with only classes."""
        classes_path = self.test_data_dir / "classes_only.py"
        content = classes_path.read_text()

        result = self.parser.parse(content, classes_path)

        class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]
        assert len(class_chunks) >= 5  # Should have multiple classes

        class_names = [chunk["metadata"]["class_name"] for chunk in class_chunks]
        assert "BaseClass" in class_names
        assert "AbstractBaseClass" in class_names
        assert "ConcreteClass" in class_names
        assert "MultipleInheritanceClass" in class_names
        assert "GenericClass" in class_names

        # Check inheritance detection
        concrete_chunk = next(chunk for chunk in class_chunks
                            if chunk["metadata"]["class_name"] == "ConcreteClass")
        if "base_classes" in concrete_chunk["metadata"]:
            assert "AbstractBaseClass" in concrete_chunk["metadata"]["base_classes"]

        # Check multiple inheritance
        multi_inherit_chunk = next(chunk for chunk in class_chunks
                                 if chunk["metadata"]["class_name"] == "MultipleInheritanceClass")
        if "base_classes" in multi_inherit_chunk["metadata"]:
            base_classes = multi_inherit_chunk["metadata"]["base_classes"]
            assert "BaseClass" in base_classes
            assert "ConcreteClass" in base_classes

    def test_parse_imports_and_module_code(self):
        """Test parsing a file with complex imports and module-level code."""
        imports_path = self.test_data_dir / "imports_and_module_code.py"
        content = imports_path.read_text()

        result = self.parser.parse(content, imports_path)

        # Check that imports are extracted
        all_imports = []
        for chunk in result.chunks:
            if "imports" in chunk["metadata"]:
                all_imports.extend(chunk["metadata"]["imports"])

        if all_imports:
            import_text = " ".join(all_imports)
            assert "import os" in import_text
            assert "from pathlib import Path" in import_text
            assert "from typing import" in import_text

        # Should have at least one chunk (module-level or fallback)
        assert len(result.chunks) >= 1

    def test_parse_no_functions_or_classes(self):
        """Test parsing a file with no functions or classes."""
        no_funcs_path = self.test_data_dir / "no_functions_or_classes.py"
        content = no_funcs_path.read_text()

        result = self.parser.parse(content, no_funcs_path)

        # Should still parse successfully
        assert len(result.chunks) >= 1
        assert len(result.errors) == 0

        # Should not have function or class chunks
        func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
        class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]

        assert len(func_chunks) == 0
        assert len(class_chunks) == 0

    def test_parse_empty_file(self):
        """Test parsing an empty Python file."""
        empty_path = self.test_data_dir / "empty.py"
        content = empty_path.read_text()

        result = self.parser.parse(content, empty_path)

        assert isinstance(result, ParseResult)
        assert result.metadata["language"] == "python"
        assert len(result.errors) == 0

    def test_parse_whitespace_only_file(self):
        """Test parsing a file with only whitespace."""
        whitespace_path = self.test_data_dir / "whitespace_only.py"
        content = whitespace_path.read_text()

        result = self.parser.parse(content, whitespace_path)

        assert isinstance(result, ParseResult)
        assert result.metadata["language"] == "python"

    def test_parser_type_metadata(self):
        """Test that parser type is correctly identified in metadata."""
        simple_path = self.test_data_dir / "simple_module.py"
        content = simple_path.read_text()

        result = self.parser.parse(content, simple_path)

        # Should indicate whether tree-sitter or regex parsing was used
        assert "parser_type" in result.metadata
        parser_type = result.metadata["parser_type"]
        assert parser_type in ["tree-sitter", "regex"]

    def test_chunk_content_accuracy(self):
        """Test that chunk content accurately reflects the source code."""
        functions_path = self.test_data_dir / "functions_only.py"
        content = functions_path.read_text()
        lines = content.split('\n')

        result = self.parser.parse(content, functions_path)

        # Check that chunk content matches line ranges
        for chunk in result.chunks:
            start_line = chunk["metadata"]["start_line"]
            end_line = chunk["metadata"]["end_line"]

            if start_line and end_line:
                # Extract expected content from original
                expected_lines = lines[start_line-1:end_line]
                expected_content = '\n'.join(expected_lines)

                # Chunk content should match or be a subset
                chunk_content = chunk["content"]
                assert len(chunk_content) > 0
                # Basic sanity check - chunk should contain some of the expected content
                if expected_content.strip():
                    assert any(line.strip() in chunk_content for line in expected_lines if line.strip())

    def test_total_chunks_metadata(self):
        """Test that total_chunks metadata is accurate."""
        simple_path = self.test_data_dir / "simple_module.py"
        content = simple_path.read_text()

        result = self.parser.parse(content, simple_path)

        assert "total_chunks" in result.metadata
        assert result.metadata["total_chunks"] == len(result.chunks)

    def test_file_path_consistency(self):
        """Test that file paths are consistent across all chunks."""
        classes_path = self.test_data_dir / "classes_only.py"
        content = classes_path.read_text()

        result = self.parser.parse(content, classes_path)

        # Check result metadata
        assert result.metadata["file_path"] == str(classes_path)

        # Check all chunk metadata
        for chunk in result.chunks:
            assert chunk["metadata"]["file_path"] == str(classes_path)
            assert chunk["metadata"]["language"] == "python"
