"""
.gitignore Parsing and Pattern Matching

This module provides functionality to parse .gitignore files and check
if files should be ignored based on gitignore patterns.

Key classes:
- GitignoreParser: Main parser for .gitignore files
- GitignorePattern: Individual pattern representation
"""

import re
from pathlib import Path
from typing import List, Set, Optional, Union
import logging

logger = logging.getLogger(__name__)


class GitignorePattern:
    """Represents a single gitignore pattern."""

    def __init__(self, pattern: str, base_path: Path):
        """
        Initialize gitignore pattern.

        Args:
            pattern: The gitignore pattern string
            base_path: Base directory path for relative patterns
        """
        self.original_pattern = pattern.strip()
        self.base_path = base_path
        self.is_negation = pattern.startswith("!")
        self.is_directory_only = pattern.endswith("/")

        # Clean up the pattern
        self.pattern = pattern.strip()
        if self.is_negation:
            self.pattern = self.pattern[1:]  # Remove !
        if self.is_directory_only:
            self.pattern = self.pattern[:-1]  # Remove trailing /

        # Convert to regex
        self.regex = self._pattern_to_regex(self.pattern)

    def _pattern_to_regex(self, pattern: str) -> re.Pattern:
        """Convert gitignore pattern to regex."""
        # Escape special regex characters except * and ?
        escaped = re.escape(pattern)

        # Convert gitignore wildcards to regex
        escaped = escaped.replace(r"\*\*", ".*")  # ** matches any number of directories
        escaped = escaped.replace(r"\*", "[^/]*")  # * matches anything except /
        escaped = escaped.replace(r"\?", "[^/]")  # ? matches single character except /

        # Handle leading slash (absolute path from repo root)
        if pattern.startswith("/"):
            escaped = "^" + escaped[1:]  # Remove leading slash, anchor to start
        else:
            # Pattern can match at any level
            escaped = "(^|/)" + escaped

        # Handle trailing patterns
        if not pattern.endswith("/"):
            escaped = escaped + "(/.*)?$"
        else:
            escaped = escaped + "$"

        try:
            return re.compile(escaped)
        except re.error as e:
            logger.warning(f"Invalid gitignore pattern '{pattern}': {e}")
            # Fallback to simple string matching
            return re.compile(re.escape(pattern))

    def matches(self, file_path: Path, is_directory: bool = False) -> bool:
        """
        Check if this pattern matches the given file path.

        Args:
            file_path: Path to check (relative to repository root)
            is_directory: Whether the path is a directory

        Returns:
            True if pattern matches
        """
        # Convert to string with forward slashes
        path_str = str(file_path).replace("\\", "/")

        # Directory-only patterns only match directories
        if self.is_directory_only and not is_directory:
            return False

        # Check regex match
        return bool(self.regex.search(path_str))


class GitignoreParser:
    """Parser for .gitignore files with pattern matching."""

    def __init__(self, repo_root: Path):
        """
        Initialize gitignore parser.

        Args:
            repo_root: Root directory of the repository
        """
        self.repo_root = Path(repo_root)
        self.patterns: List[GitignorePattern] = []
        self.global_patterns: List[GitignorePattern] = []

        # Load patterns from .gitignore files
        self._load_gitignore_files()

        # Add default ignore patterns
        self._add_default_patterns()

    def _load_gitignore_files(self) -> None:
        """Load patterns from .gitignore files in the repository."""
        # Load root .gitignore
        root_gitignore = self.repo_root / ".gitignore"
        if root_gitignore.exists():
            self._load_gitignore_file(root_gitignore, self.repo_root)

        # Load nested .gitignore files
        for gitignore_file in self.repo_root.rglob(".gitignore"):
            if gitignore_file != root_gitignore:
                relative_dir = gitignore_file.parent
                self._load_gitignore_file(gitignore_file, relative_dir)

    def _load_gitignore_file(self, gitignore_path: Path, base_path: Path) -> None:
        """Load patterns from a specific .gitignore file."""
        try:
            with open(gitignore_path, "r", encoding="utf-8", errors="ignore") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith("#"):
                    continue

                # Create pattern
                pattern = GitignorePattern(line, base_path)
                self.patterns.append(pattern)

            logger.debug(f"Loaded {len(lines)} patterns from {gitignore_path}")

        except Exception as e:
            logger.warning(f"Error reading .gitignore file {gitignore_path}: {e}")

    def _add_default_patterns(self) -> None:
        """Add default ignore patterns."""
        default_patterns = [
            # Version control
            ".git/",
            ".svn/",
            ".hg/",
            # Python
            "__pycache__/",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            ".Python",
            "pip-log.txt",
            "pip-delete-this-directory.txt",
            ".pytest_cache/",
            ".coverage",
            ".mypy_cache/",
            # Node.js
            "node_modules/",
            "npm-debug.log*",
            "yarn-debug.log*",
            "yarn-error.log*",
            # Build directories
            "build/",
            "dist/",
            "target/",
            "out/",
            # IDE files
            ".vscode/",
            ".idea/",
            "*.swp",
            "*.swo",
            "*~",
            # OS files
            ".DS_Store",
            "Thumbs.db",
            "desktop.ini",
            # Temporary files
            "*.tmp",
            "*.temp",
            "*.log",
            # Environment files
            ".env",
            ".env.local",
            ".env.*.local",
        ]

        for pattern_str in default_patterns:
            pattern = GitignorePattern(pattern_str, self.repo_root)
            self.global_patterns.append(pattern)

    def should_ignore(self, file_path: Path, is_directory: Optional[bool] = None) -> bool:
        """
        Check if a file should be ignored based on gitignore patterns.

        Args:
            file_path: Path to check (can be absolute or relative)
            is_directory: Whether the path is a directory (auto-detected if None)

        Returns:
            True if file should be ignored
        """
        # Convert to path relative to repo root
        try:
            if file_path.is_absolute():
                rel_path = file_path.relative_to(self.repo_root)
            else:
                rel_path = file_path
        except ValueError:
            # Path is outside repo root
            return False

        # Auto-detect if it's a directory
        if is_directory is None:
            full_path = self.repo_root / rel_path
            is_directory = full_path.is_dir() if full_path.exists() else False

        # Check against all patterns
        ignored = False

        # Check global patterns first
        for pattern in self.global_patterns:
            if pattern.matches(rel_path, is_directory):
                if pattern.is_negation:
                    ignored = False
                else:
                    ignored = True

        # Check repository-specific patterns
        for pattern in self.patterns:
            if pattern.matches(rel_path, is_directory):
                if pattern.is_negation:
                    ignored = False
                else:
                    ignored = True

        return ignored

    def get_ignored_files(self, directory: Optional[Path] = None) -> Set[Path]:
        """
        Get set of all ignored files in the repository.

        Args:
            directory: Directory to scan (defaults to repo root)

        Returns:
            Set of ignored file paths
        """
        if directory is None:
            directory = self.repo_root

        ignored_files = set()

        for file_path in directory.rglob("*"):
            if self.should_ignore(file_path):
                ignored_files.add(file_path)

        return ignored_files

    def add_pattern(self, pattern: str) -> None:
        """Add a custom ignore pattern."""
        git_pattern = GitignorePattern(pattern, self.repo_root)
        self.patterns.append(git_pattern)

    def add_patterns(self, patterns: List[str]) -> None:
        """Add multiple custom ignore patterns."""
        for pattern in patterns:
            self.add_pattern(pattern)


def parse_gitignore(
    repo_path: Union[str, Path], additional_patterns: Optional[List[str]] = None
) -> GitignoreParser:
    """
    Convenience function to create a GitignoreParser.

    Args:
        repo_path: Path to repository root
        additional_patterns: Additional patterns to ignore

    Returns:
        Configured GitignoreParser instance
    """
    parser = GitignoreParser(Path(repo_path))

    if additional_patterns:
        parser.add_patterns(additional_patterns)

    return parser


def is_ignored(
    file_path: Path, repo_path: Path, additional_patterns: Optional[List[str]] = None
) -> bool:
    """
    Quick check if a file should be ignored.

    Args:
        file_path: Path to check
        repo_path: Repository root path
        additional_patterns: Additional patterns to check

    Returns:
        True if file should be ignored
    """
    parser = parse_gitignore(repo_path, additional_patterns)
    return parser.should_ignore(file_path)


def filter_ignored_files(
    file_paths: List[Path], repo_path: Path, additional_patterns: Optional[List[str]] = None
) -> List[Path]:
    """
    Filter out ignored files from a list.

    Args:
        file_paths: List of file paths to filter
        repo_path: Repository root path
        additional_patterns: Additional patterns to check

    Returns:
        List of non-ignored files
    """
    parser = parse_gitignore(repo_path, additional_patterns)

    return [file_path for file_path in file_paths if not parser.should_ignore(file_path)]
