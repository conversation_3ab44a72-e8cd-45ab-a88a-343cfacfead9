#!/usr/bin/env python3
"""
Manual test script for the Python parser.
"""

import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_python_parser():
    """Test the Python parser manually."""
    try:
        from parsers.python import PythonParser
        print("✓ PythonParser imported successfully")
        
        parser = PythonParser()
        print("✓ PythonParser instantiated successfully")
        
        # Test basic properties
        assert parser.get_language() == "python"
        print("✓ get_language() returns 'python'")
        
        extensions = parser.get_supported_extensions()
        assert extensions == [".py", ".pyw", ".pyi"]
        print("✓ get_supported_extensions() returns correct extensions")
        
        # Test parsing a simple function
        content = '''def hello_world():
    """A simple hello world function."""
    return "Hello, World!"
'''
        file_path = Path("test.py")
        result = parser.parse(content, file_path)
        
        print(f"✓ Parsed content successfully, got {len(result.chunks)} chunks")
        print(f"✓ Result metadata: {result.metadata}")
        print(f"✓ Errors: {result.errors}")
        
        # Test with test data
        test_data_dir = Path(__file__).parent / "tests" / "data" / "python"
        if test_data_dir.exists():
            simple_module_path = test_data_dir / "simple_module.py"
            if simple_module_path.exists():
                content = simple_module_path.read_text()
                result = parser.parse(content, simple_module_path)
                print(f"✓ Parsed simple_module.py successfully, got {len(result.chunks)} chunks")
                
                # Check for functions and classes
                func_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "function"]
                class_chunks = [chunk for chunk in result.chunks if chunk["metadata"]["chunk_type"] == "class"]
                
                print(f"✓ Found {len(func_chunks)} function chunks")
                print(f"✓ Found {len(class_chunks)} class chunks")
                
                if func_chunks:
                    print(f"✓ Function names: {[chunk['metadata']['function_name'] for chunk in func_chunks]}")
                if class_chunks:
                    print(f"✓ Class names: {[chunk['metadata']['class_name'] for chunk in class_chunks]}")
            else:
                print("⚠ simple_module.py test data not found")
        else:
            print("⚠ Test data directory not found")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_python_parser()
    sys.exit(0 if success else 1)
