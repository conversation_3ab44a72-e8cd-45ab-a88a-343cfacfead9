# Core dependencies for RAG Indexer Phase 1
# Tree-sitter for semantic parsing
# TODO these need to be updated to current
tree-sitter>=0.20.4
tree-sitter-python>=0.20.4
tree-sitter-javascript>=0.20.3

# Configuration and data validation
pydantic>=2.4.2
pyyaml>=6.0.1

# CLI interface
click>=8.1.7

# Progress tracking and utilities
tqdm>=4.66.1

# Git integration for .gitignore parsing
gitpython>=3.1.40

# Optional dependencies for Phase 2 (embedding and storage)
# Uncomment when implementing Phase 2:
# sentence-transformers>=2.2.2
# chromadb>=0.4.15
# openai>=1.0.0  # For OpenAI embeddings

# Development dependencies (uncomment for development)
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0
