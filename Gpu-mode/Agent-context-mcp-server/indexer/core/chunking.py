"""
Code Parsing and Chunking

This module handles the semantic parsing and chunking of source code files.
It uses tree-sitter parsers to extract meaningful code structures like
functions, classes, and documentation blocks.

Key classes:
- CodeChunk: Represents a semantic chunk of code
- ChunkMetadata: Metadata associated with each chunk
- ChunkingEngine: Main orchestrator for chunking operations
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from pathlib import Path
import logging

from .traversal import FileType

logger = logging.getLogger(__name__)


@dataclass
class ChunkMetadata:
    """Metadata for a code chunk."""

    file_path: str
    start_line: int
    end_line: int
    chunk_type: str  # 'function', 'class', 'documentation', etc.
    language: str
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    docstring: Optional[str] = None
    imports: List[str] = field(default_factory=list)
    decorators: List[str] = field(default_factory=list)
    complexity_score: Optional[float] = None


@dataclass
class CodeChunk:
    """Represents a semantic chunk of code."""

    content: str
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str
    language: str
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_embedding_text(self) -> str:
        """
        Convert chunk to text suitable for embedding.

        Returns:
            Formatted text including context and content
        """
        context_parts = [f"File: {self.file_path}"]

        if self.metadata.get("function_name"):
            context_parts.append(f"Function: {self.metadata['function_name']}")
        if self.metadata.get("class_name"):
            context_parts.append(f"Class: {self.metadata['class_name']}")
        if self.metadata.get("docstring"):
            context_parts.append(f"Description: {self.metadata['docstring']}")

        context = "\n".join(context_parts)
        return f"{context}\n\n{self.content}"

    def get_unique_id(self) -> str:
        """Generate unique identifier for this chunk."""
        return f"{self.file_path}:{self.start_line}:{self.end_line}"

    @classmethod
    def from_metadata(cls, content: str, metadata: ChunkMetadata) -> "CodeChunk":
        """Create CodeChunk from ChunkMetadata."""
        return cls(
            content=content,
            file_path=metadata.file_path,
            start_line=metadata.start_line,
            end_line=metadata.end_line,
            chunk_type=metadata.chunk_type,
            language=metadata.language,
            metadata={
                "function_name": metadata.function_name,
                "class_name": metadata.class_name,
                "docstring": metadata.docstring,
                "imports": metadata.imports,
                "decorators": metadata.decorators,
                "complexity_score": metadata.complexity_score,
            },
        )


class ChunkingEngine:
    """Main engine for chunking source code files."""

    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        """
        Initialize chunking engine.

        Args:
            max_chunk_size: Maximum size of chunks in tokens
            overlap_size: Overlap between adjacent chunks in tokens
        """
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
        self._parsers = {}

    def chunk_file(self, file_path: Path, content: str, file_type: FileType) -> List[CodeChunk]:
        """
        Chunk a file into semantic pieces.

        Args:
            file_path: Path to the file
            content: File content as string
            file_type: Type of file to determine parsing strategy

        Returns:
            List of code chunks
        """
        logger.debug(f"Chunking file: {file_path} (type: {file_type})")

        try:
            if file_type in [FileType.PYTHON, FileType.JAVASCRIPT, FileType.TYPESCRIPT]:
                return self._chunk_source_code(file_path, content, file_type)
            elif file_type == FileType.MARKDOWN:
                return self._chunk_documentation(file_path, content)
            elif file_type in [FileType.JSON, FileType.YAML, FileType.TOML]:
                return self._chunk_configuration(file_path, content, file_type)
            else:
                return self._chunk_generic(file_path, content)

        except Exception as e:
            logger.error(f"Error chunking file {file_path}: {e}")
            # Fallback to generic chunking
            return self._chunk_generic(file_path, content)

    def _chunk_source_code(
        self, file_path: Path, content: str, file_type: FileType
    ) -> List[CodeChunk]:
        """Chunk source code using tree-sitter parsers."""
        # TODO: Implement tree-sitter based chunking
        # For now, return a simple line-based chunk
        lines = content.split("\n")
        chunks = []

        chunk = CodeChunk(
            content=content,
            file_path=str(file_path),
            start_line=1,
            end_line=len(lines),
            chunk_type="file",
            language=file_type.value,
            metadata={"total_lines": len(lines)},
        )
        chunks.append(chunk)

        return chunks

    def _chunk_documentation(self, file_path: Path, content: str) -> List[CodeChunk]:
        """Chunk documentation files by headers."""
        # TODO: Implement markdown header-based chunking
        lines = content.split("\n")

        chunk = CodeChunk(
            content=content,
            file_path=str(file_path),
            start_line=1,
            end_line=len(lines),
            chunk_type="documentation",
            language="markdown",
            metadata={"total_lines": len(lines)},
        )

        return [chunk]

    def _chunk_configuration(
        self, file_path: Path, content: str, file_type: FileType
    ) -> List[CodeChunk]:
        """Chunk configuration files."""
        lines = content.split("\n")

        chunk = CodeChunk(
            content=content,
            file_path=str(file_path),
            start_line=1,
            end_line=len(lines),
            chunk_type="configuration",
            language=file_type.value,
            metadata={"total_lines": len(lines)},
        )

        return [chunk]

    def _chunk_generic(self, file_path: Path, content: str) -> List[CodeChunk]:
        """Generic line-based chunking for unsupported file types."""
        lines = content.split("\n")
        chunks = []

        # Split into chunks of reasonable size
        chunk_size = 50  # lines per chunk
        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i : i + chunk_size]
            chunk_content = "\n".join(chunk_lines)

            chunk = CodeChunk(
                content=chunk_content,
                file_path=str(file_path),
                start_line=i + 1,
                end_line=min(i + chunk_size, len(lines)),
                chunk_type="generic",
                language="text",
                metadata={"chunk_lines": len(chunk_lines)},
            )
            chunks.append(chunk)

        return chunks


def chunk_source_code(content: str, language: str) -> List[CodeChunk]:
    """
    Convenience function to chunk source code.

    Args:
        content: Source code content
        language: Programming language

    Returns:
        List of code chunks
    """
    engine = ChunkingEngine()
    file_type = FileType.UNKNOWN

    # Map language to file type
    lang_map = {
        "python": FileType.PYTHON,
        "javascript": FileType.JAVASCRIPT,
        "typescript": FileType.TYPESCRIPT,
        "markdown": FileType.MARKDOWN,
    }

    file_type = lang_map.get(language.lower(), FileType.UNKNOWN)
    return engine.chunk_file(Path("unknown"), content, file_type)
