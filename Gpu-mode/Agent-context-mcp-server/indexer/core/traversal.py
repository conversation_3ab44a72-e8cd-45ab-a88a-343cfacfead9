"""
File Discovery and Filtering

This module handles the traversal of repository directories to discover
relevant files for indexing. It respects .gitignore patterns and provides
configurable filtering based on file types, sizes, and custom patterns.

Key functions:
- discover_files: Recursively scan repository for relevant files
- should_ignore_file: Check if file should be ignored based on patterns
- get_file_type: Determine file type for processing strategy
"""

from pathlib import Path
from typing import List, Set, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class FileType(Enum):
    """Supported file types for indexing."""

    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    MARKDOWN = "markdown"
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"
    TEXT = "text"
    UNKNOWN = "unknown"


# File extension mappings
EXTENSION_MAP = {
    ".py": FileType.PYTHON,
    ".js": FileType.JAVASCRIPT,
    ".jsx": FileType.JAVASCRIPT,
    ".ts": FileType.TYPESCRIPT,
    ".tsx": FileType.TYPESCRIPT,
    ".md": FileType.MARKDOWN,
    ".markdown": FileType.MARKDOWN,
    ".json": FileType.JSON,
    ".yaml": FileType.YAML,
    ".yml": FileType.YAML,
    ".toml": FileType.TOML,
    ".txt": FileType.TEXT,
    ".rst": FileType.TEXT,
}


def discover_files(
    repo_path: Path,
    ignore_patterns: List[str],
    include_extensions: Optional[Set[str]] = None,
    max_file_size_mb: float = 10.0,
) -> List[Path]:
    """
    Recursively discover files in repository for indexing.

    Args:
        repo_path: Path to repository root
        ignore_patterns: List of patterns to ignore (gitignore style)
        include_extensions: Set of file extensions to include (None = all supported)
        max_file_size_mb: Maximum file size in MB to process

    Returns:
        List of file paths to process
    """
    files = []
    max_size_bytes = max_file_size_mb * 1024 * 1024

    logger.info(f"Discovering files in {repo_path}")

    for file_path in repo_path.rglob("*"):
        if not file_path.is_file():
            continue

        # Check if file should be ignored
        if should_ignore_file(file_path, ignore_patterns):
            continue

        # Check file size
        try:
            if file_path.stat().st_size > max_size_bytes:
                file_size_mb = file_path.stat().st_size / 1024 / 1024
                logger.warning(f"Skipping large file: {file_path} ({file_size_mb:.1f}MB)")
                continue
        except OSError:
            logger.warning(f"Could not stat file: {file_path}")
            continue

        # Check file extension
        if include_extensions and file_path.suffix not in include_extensions:
            continue

        # Check if we support this file type
        file_type = get_file_type(file_path)
        if file_type == FileType.UNKNOWN:
            continue

        files.append(file_path)

    logger.info(f"Discovered {len(files)} files for processing")
    return files


def should_ignore_file(file_path: Path, ignore_patterns: List[str]) -> bool:
    """
    Check if file should be ignored based on patterns.

    Args:
        file_path: Path to check
        ignore_patterns: List of ignore patterns

    Returns:
        True if file should be ignored
    """
    file_str = str(file_path)

    # Check each ignore pattern
    for pattern in ignore_patterns:
        if pattern in file_str:
            return True

    # Default ignore patterns
    default_ignores = [
        ".git/",
        "__pycache__/",
        "node_modules/",
        ".venv/",
        "venv/",
        ".env/",
        "dist/",
        "build/",
        ".pytest_cache/",
        ".mypy_cache/",
        ".coverage",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".DS_Store",
    ]

    for pattern in default_ignores:
        if pattern.endswith("/"):
            # Directory pattern
            if f"/{pattern}" in f"/{file_str}" or file_str.startswith(pattern):
                return True
        elif pattern.startswith("*."):
            # Extension pattern
            if file_path.suffix == pattern[1:]:
                return True
        else:
            # Filename pattern
            if pattern in file_str:
                return True

    return False


def get_file_type(file_path: Path) -> FileType:
    """
    Determine file type based on extension and content.

    Args:
        file_path: Path to file

    Returns:
        FileType enum value
    """
    extension = file_path.suffix.lower()
    return EXTENSION_MAP.get(extension, FileType.UNKNOWN)


def is_text_file(file_path: Path) -> bool:
    """
    Check if file is a text file by examining content.

    Args:
        file_path: Path to file

    Returns:
        True if file appears to be text
    """
    try:
        with open(file_path, "rb") as f:
            chunk = f.read(1024)

        # Check for null bytes (binary indicator)
        if b"\x00" in chunk:
            return False

        # Try to decode as UTF-8
        try:
            chunk.decode("utf-8")
            return True
        except UnicodeDecodeError:
            return False

    except (OSError, IOError):
        return False
