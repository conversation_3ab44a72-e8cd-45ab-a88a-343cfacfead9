"""
Core Indexer Functionality

This package contains the core components of the RAG indexer:
- traversal: File discovery and filtering
- chunking: Code parsing and semantic chunking
- embedding: Vector generation from code chunks
- storage: Database operations for vector storage
- indexer: Main orchestrator class
"""

from .chunking import CodeChunk, ChunkMetadata

# CodebaseIndexer requires Phase 2 dependencies (embedding, storage)
# Import it conditionally to avoid import errors in Phase 1
try:
    from .indexer import CodebaseIndexer  # noqa: F401

    __all__ = ["CodeChunk", "ChunkMetadata", "CodebaseIndexer"]
except ImportError:
    __all__ = ["CodeChunk", "ChunkMetadata"]
