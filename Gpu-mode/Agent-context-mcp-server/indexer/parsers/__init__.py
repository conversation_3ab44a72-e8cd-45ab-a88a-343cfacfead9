"""
Language-Specific Parsers

This package contains parsers for different programming languages and file types.
Each parser uses tree-sitter or other appropriate parsing techniques to extract
semantic information from source code.

Parsers:
- base: Abstract parser interface
- python: Python-specific parsing using tree-sitter
- javascript: JavaScript/TypeScript parsing
- markdown: Documentation parsing
- generic: Fallback parser for unsupported types
"""

from .base import BaseParser, ParserRegistry
from .python import PythonParser
from .javascript import JavaScriptParser
from .markdown import MarkdownParser
from .generic import GenericParser

# Initialize default parser registry
default_registry = ParserRegistry()
default_registry.register("python", PythonParser)
default_registry.register("javascript", JavaScriptParser)
default_registry.register("typescript", JavaScriptParser)
default_registry.register("markdown", MarkdownParser)
default_registry.register("generic", GenericParser)

__all__ = [
    "BaseParser",
    "ParserRegistry",
    "PythonParser",
    "JavaScriptParser",
    "MarkdownParser",
    "GenericParser",
    "default_registry",
]
