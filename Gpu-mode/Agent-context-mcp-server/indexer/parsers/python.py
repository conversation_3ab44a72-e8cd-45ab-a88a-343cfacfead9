"""
Python Parser Implementation

This module provides Python-specific parsing using tree-sitter to extract
semantic information from Python source code files.

Key features:
- Function and method extraction with docstrings
- Class extraction with inheritance information
- Import statement parsing
- Decorator handling
- Type hint extraction
"""

import re
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

from .base import BaseParser, ParseResult

logger = logging.getLogger(__name__)


class PythonParser(BaseParser):
    """Parser for Python source code using tree-sitter."""

    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        """Initialize Python parser."""
        super().__init__(max_chunk_size, overlap_size)
        self._parser: Optional[Any] = None
        self._language: Optional[Any] = None

    def get_language(self) -> str:
        """Get language identifier."""
        return "python"

    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return [".py", ".pyw", ".pyi"]

    def _init_tree_sitter(self) -> None:
        """Initialize tree-sitter parser (lazy loading)."""
        if self._parser is None:
            try:
                import tree_sitter_python as tspython
                from tree_sitter import Language, Parser

                self._language = Language(tspython.language())
                self._parser = Parser(self._language)
                logger.debug("Initialized tree-sitter Python parser")

            except ImportError:
                logger.warning("tree-sitter-python not available, falling back to regex parsing")
                self._parser = "regex"  # Flag for regex fallback

    def parse(self, content: str, file_path: Path) -> ParseResult:
        """
        Parse Python content and extract semantic chunks.

        Args:
            content: Python source code
            file_path: Path to the Python file

        Returns:
            ParseResult with extracted chunks
        """
        self._init_tree_sitter()

        chunks = []
        errors = []

        try:
            if self._parser == "regex":
                # Fallback to regex-based parsing
                chunks = self._parse_with_regex(content, file_path)
            else:
                # Use tree-sitter parsing
                chunks = self._parse_with_tree_sitter(content, file_path)

        except Exception as e:
            logger.error(f"Error parsing Python file {file_path}: {e}")
            errors.append(str(e))
            # Fallback to simple chunking
            chunks = self._fallback_parse(content, file_path)

        metadata = {
            "language": "python",
            "file_path": str(file_path),
            "total_chunks": len(chunks),
            "parser_type": "tree-sitter" if self._parser != "regex" else "regex",
        }

        return ParseResult(chunks=chunks, metadata=metadata, errors=errors)

    def _parse_with_tree_sitter(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse using tree-sitter."""
        if self._parser is None or self._parser == "regex":
            return []
        tree = self._parser.parse(bytes(content, "utf8"))
        chunks = []

        # Extract imports first
        imports = self._extract_imports_tree_sitter(tree, content)

        # Extract functions and classes
        chunks.extend(self._extract_functions_tree_sitter(tree, content, file_path, imports))
        chunks.extend(self._extract_classes_tree_sitter(tree, content, file_path, imports))

        # Extract module-level code
        module_chunk = self._extract_module_level_code(tree, content, file_path, imports)
        if module_chunk:
            chunks.append(module_chunk)

        return chunks

    def _parse_with_regex(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Fallback regex-based parsing."""
        chunks = []
        lines = content.split("\n")

        # Extract imports
        imports = self.extract_imports(content)

        # Find functions
        func_pattern = r"^(\s*)def\s+(\w+)\s*\([^)]*\).*?:"
        for i, line in enumerate(lines):
            match = re.match(func_pattern, line)
            if match:
                indent, func_name = match.groups()

                # Find function end
                func_lines = [line]
                j = i + 1
                base_indent = len(indent)

                while j < len(lines):
                    current_line = lines[j]
                    if current_line.strip() == "":
                        func_lines.append(current_line)
                    elif len(current_line) - len(current_line.lstrip()) > base_indent:
                        func_lines.append(current_line)
                    else:
                        break
                    j += 1

                func_content = "\n".join(func_lines)
                docstring = self._extract_docstring_regex(func_content)

                chunk = {
                    "content": func_content,
                    "metadata": {
                        "chunk_type": "function",
                        "function_name": func_name,
                        "start_line": i + 1,
                        "end_line": i + len(func_lines),
                        "docstring": docstring,
                        "imports": imports,
                        "language": "python",
                        "file_path": str(file_path),
                    },
                }
                chunks.append(chunk)

        return chunks

    def _extract_functions_tree_sitter(
        self, tree, content: str, file_path: Path, imports: List[str]
    ) -> List[Dict[str, Any]]:
        """Extract functions using tree-sitter."""
        chunks = []

        def traverse(node, depth=0):
            if node.type == "function_def":
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                func_content = content[node.start_byte : node.end_byte]

                # Extract function name
                name_node = node.child_by_field_name("name")
                func_name = (
                    content[name_node.start_byte : name_node.end_byte] if name_node else "unknown"
                )

                # Extract docstring
                docstring = self._extract_docstring_tree_sitter(node, content)

                # Extract decorators
                decorators = self._extract_decorators_tree_sitter(node, content)

                chunk = {
                    "content": func_content,
                    "metadata": {
                        "chunk_type": "function",
                        "function_name": func_name,
                        "start_line": start_line,
                        "end_line": end_line,
                        "docstring": docstring,
                        "decorators": decorators,
                        "imports": imports,
                        "language": "python",
                        "file_path": str(file_path),
                        "complexity_score": self.calculate_complexity(func_content),
                    },
                }
                chunks.append(chunk)

            for child in node.children:
                traverse(child, depth + 1)

        traverse(tree.root_node)
        return chunks

    def _extract_classes_tree_sitter(
        self, tree, content: str, file_path: Path, imports: List[str]
    ) -> List[Dict[str, Any]]:
        """Extract classes using tree-sitter."""
        chunks = []

        def traverse(node, depth=0):
            if node.type == "class_definition":
                start_line = node.start_point[0] + 1
                end_line = node.end_point[0] + 1
                class_content = content[node.start_byte : node.end_byte]

                # Extract class name
                name_node = node.child_by_field_name("name")
                class_name = (
                    content[name_node.start_byte : name_node.end_byte] if name_node else "unknown"
                )

                # Extract docstring
                docstring = self._extract_docstring_tree_sitter(node, content)

                # Extract base classes
                bases = self._extract_base_classes_tree_sitter(node, content)

                chunk = {
                    "content": class_content,
                    "metadata": {
                        "chunk_type": "class",
                        "class_name": class_name,
                        "start_line": start_line,
                        "end_line": end_line,
                        "docstring": docstring,
                        "base_classes": bases,
                        "imports": imports,
                        "language": "python",
                        "file_path": str(file_path),
                        "complexity_score": self.calculate_complexity(class_content),
                    },
                }
                chunks.append(chunk)

            for child in node.children:
                traverse(child, depth + 1)

        traverse(tree.root_node)
        return chunks

    def _extract_imports_tree_sitter(self, tree, content: str) -> List[str]:
        """Extract import statements using tree-sitter."""
        imports = []

        def traverse(node):
            if node.type in ["import_statement", "import_from_statement"]:
                import_text = content[node.start_byte : node.end_byte]
                imports.append(import_text.strip())

            for child in node.children:
                traverse(child)

        traverse(tree.root_node)
        return imports

    def _extract_docstring_tree_sitter(self, node, content: str) -> Optional[str]:
        """Extract docstring from a function or class node."""
        # Look for string literal as first statement in body
        for child in node.children:
            if child.type == "block":
                for stmt in child.children:
                    if stmt.type == "expression_statement":
                        for expr in stmt.children:
                            if expr.type == "string":
                                docstring = content[expr.start_byte : expr.end_byte]
                                # Clean up the docstring
                                return self._clean_docstring(docstring)
        return None

    def _extract_decorators_tree_sitter(self, node, content: str) -> List[str]:
        """Extract decorators from a function node."""
        decorators = []

        # Look for decorator nodes before the function
        for child in node.children:
            if child.type == "decorator":
                decorator_text = content[child.start_byte : child.end_byte]
                decorators.append(decorator_text.strip())

        return decorators

    def _extract_base_classes_tree_sitter(self, node, content: str) -> List[str]:
        """Extract base classes from a class node."""
        bases = []

        # Look for argument list in class definition
        for child in node.children:
            if child.type == "argument_list":
                for arg in child.children:
                    if arg.type == "identifier":
                        base_name = content[arg.start_byte : arg.end_byte]
                        bases.append(base_name)

        return bases

    def _extract_module_level_code(
        self, tree, content: str, file_path: Path, imports: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Extract module-level code (non-function, non-class)."""
        # TODO: Implement module-level code extraction
        return None

    def _clean_docstring(self, docstring: str) -> str:
        """Clean and format docstring."""
        # Remove quotes
        docstring = docstring.strip()
        if docstring.startswith('"""') or docstring.startswith("'''"):
            docstring = docstring[3:-3]
        elif docstring.startswith('"') or docstring.startswith("'"):
            docstring = docstring[1:-1]

        # Clean up whitespace
        lines = docstring.split("\n")
        cleaned_lines = [line.strip() for line in lines if line.strip()]
        return " ".join(cleaned_lines)

    def _extract_docstring_regex(self, content: str) -> Optional[str]:
        """Extract docstring using regex."""
        # Look for triple-quoted strings at the beginning
        pattern = r'^\s*"""(.*?)"""|^\s*\'\'\'(.*?)\'\'\''
        match = re.search(pattern, content, re.DOTALL | re.MULTILINE)
        if match:
            return (match.group(1) or match.group(2)).strip()
        return None

    def _fallback_parse(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Simple fallback parsing when tree-sitter fails."""
        lines = content.split("\n")

        chunk = {
            "content": content,
            "metadata": {
                "chunk_type": "file",
                "start_line": 1,
                "end_line": len(lines),
                "language": "python",
                "file_path": str(file_path),
                "total_lines": len(lines),
                "parsing_method": "fallback",
            },
        }

        return [chunk]

    def extract_imports(self, content: str) -> List[str]:
        """Extract import statements using regex."""
        imports = []

        # Match import statements
        import_pattern = r"^(import\s+[\w\.,\s]+|from\s+[\w\.]+\s+import\s+[\w\.,\s\*]+)"

        for line in content.split("\n"):
            line = line.strip()
            if re.match(import_pattern, line):
                imports.append(line)

        return imports
