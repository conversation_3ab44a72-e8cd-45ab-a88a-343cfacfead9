"""
Markdown Parser Implementation

This module provides Markdown-specific parsing to extract semantic
information from documentation files.

Key features:
- Header-based section extraction
- Code block extraction with language detection
- Table and list extraction
- Link and reference extraction
"""

import re
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

from .base import BaseParser, ParseResult

logger = logging.getLogger(__name__)


class MarkdownParser(BaseParser):
    """Parser for Markdown documentation files."""

    def get_language(self) -> str:
        """Get language identifier."""
        return "markdown"

    def get_supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return [".md", ".markdown", ".mdown", ".mkd", ".rst"]

    def parse(self, content: str, file_path: Path) -> ParseResult:
        """
        Parse Markdown content and extract semantic chunks.

        Args:
            content: Markdown content
            file_path: Path to the Markdown file

        Returns:
            ParseResult with extracted chunks
        """
        chunks = []
        errors = []

        try:
            if file_path.suffix.lower() == ".rst":
                chunks = self._parse_rst(content, file_path)
            else:
                chunks = self._parse_markdown(content, file_path)

        except Exception as e:
            logger.error(f"Error parsing Markdown file {file_path}: {e}")
            errors.append(str(e))
            # Fallback to simple chunking
            chunks = self._fallback_parse(content, file_path)

        metadata = {
            "language": "markdown",
            "file_path": str(file_path),
            "total_chunks": len(chunks),
            "file_type": "rst" if file_path.suffix.lower() == ".rst" else "markdown",
        }

        return ParseResult(chunks=chunks, metadata=metadata, errors=errors)

    def _parse_markdown(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse Markdown content."""
        chunks = []
        lines = content.split("\n")

        # Find all headers
        headers = self._find_headers(lines)

        if not headers:
            # No headers found, treat as single chunk
            return self._create_single_chunk(content, file_path)

        # Split content by headers
        for i, (level, title, line_num) in enumerate(headers):
            # Determine section end
            if i + 1 < len(headers):
                end_line = headers[i + 1][2] - 1
            else:
                end_line = len(lines)

            # Extract section content
            section_lines = lines[line_num:end_line]
            section_content = "\n".join(section_lines)

            # Extract code blocks from this section
            code_blocks = self._extract_code_blocks(section_content)

            # Extract links
            links = self._extract_links(section_content)

            chunk = {
                "content": section_content,
                "metadata": {
                    "chunk_type": "documentation_section",
                    "header_level": level,
                    "header_title": title,
                    "start_line": line_num + 1,
                    "end_line": end_line,
                    "code_blocks": code_blocks,
                    "links": links,
                    "language": "markdown",
                    "file_path": str(file_path),
                },
            }
            chunks.append(chunk)

        return chunks

    def _parse_rst(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Parse reStructuredText content."""
        # Simple RST parsing - treat as single chunk for now
        # TODO: Implement proper RST header detection
        return self._create_single_chunk(content, file_path, "rst")

    def _find_headers(self, lines: List[str]) -> List[Tuple[int, str, int]]:
        """
        Find all headers in Markdown content.

        Returns:
            List of tuples (level, title, line_number)
        """
        headers = []

        for i, line in enumerate(lines):
            # ATX headers (# ## ###)
            atx_match = re.match(r"^(#{1,6})\s+(.+)", line.strip())
            if atx_match:
                level = len(atx_match.group(1))
                title = atx_match.group(2).strip()
                headers.append((level, title, i))
                continue

            # Setext headers (underlined with = or -)
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and all(c in "=" for c in next_line) and len(next_line) >= 3:
                    # H1 header
                    headers.append((1, line.strip(), i))
                elif next_line and all(c in "-" for c in next_line) and len(next_line) >= 3:
                    # H2 header
                    headers.append((2, line.strip(), i))

        return headers

    def _extract_code_blocks(self, content: str) -> List[Dict[str, str]]:
        """Extract code blocks from Markdown content."""
        code_blocks = []

        # Fenced code blocks (```)
        fenced_pattern = r"```(\w+)?\n(.*?)\n```"
        for match in re.finditer(fenced_pattern, content, re.DOTALL):
            language = match.group(1) or "text"
            code = match.group(2)
            code_blocks.append({"language": language, "code": code, "type": "fenced"})

        # Indented code blocks (4 spaces)
        lines = content.split("\n")
        in_code_block = False
        current_code = []

        for line in lines:
            if line.startswith("    ") and line.strip():
                # Code line
                if not in_code_block:
                    in_code_block = True
                    current_code = []
                current_code.append(line[4:])  # Remove 4-space indent
            else:
                # Non-code line
                if in_code_block:
                    # End of code block
                    code_blocks.append(
                        {"language": "text", "code": "\n".join(current_code), "type": "indented"}
                    )
                    in_code_block = False

        # Handle code block at end of content
        if in_code_block:
            code_blocks.append(
                {"language": "text", "code": "\n".join(current_code), "type": "indented"}
            )

        return code_blocks

    def _extract_links(self, content: str) -> List[Dict[str, str]]:
        """Extract links from Markdown content."""
        links = []

        # Markdown links [text](url)
        link_pattern = r"\[([^\]]+)\]\(([^)]+)\)"
        for match in re.finditer(link_pattern, content):
            text = match.group(1)
            url = match.group(2)
            links.append({"text": text, "url": url, "type": "markdown"})

        # Reference links [text][ref]
        ref_pattern = r"\[([^\]]+)\]\[([^\]]+)\]"
        for match in re.finditer(ref_pattern, content):
            text = match.group(1)
            ref = match.group(2)
            links.append({"text": text, "reference": ref, "type": "reference"})

        # Auto links <url>
        auto_pattern = r"<(https?://[^>]+)>"
        for match in re.finditer(auto_pattern, content):
            url = match.group(1)
            links.append({"text": url, "url": url, "type": "auto"})

        return links

    def _extract_tables(self, content: str) -> List[Dict[str, Any]]:
        """Extract tables from Markdown content."""
        tables = []
        lines = content.split("\n")

        in_table = False
        table_lines = []

        for line in lines:
            # Check if line looks like a table row
            if "|" in line and line.strip().startswith("|") and line.strip().endswith("|"):
                if not in_table:
                    in_table = True
                    table_lines = []
                table_lines.append(line.strip())
            else:
                if in_table:
                    # End of table
                    if len(table_lines) >= 2:  # At least header and separator
                        tables.append(
                            {
                                "content": "\n".join(table_lines),
                                "rows": len(table_lines) - 1,  # Exclude separator
                                "type": "markdown_table",
                            }
                        )
                    in_table = False

        # Handle table at end of content
        if in_table and len(table_lines) >= 2:
            tables.append(
                {
                    "content": "\n".join(table_lines),
                    "rows": len(table_lines) - 1,
                    "type": "markdown_table",
                }
            )

        return tables

    def _extract_lists(self, content: str) -> List[Dict[str, Any]]:
        """Extract lists from Markdown content."""
        lists = []
        lines = content.split("\n")

        in_list = False
        list_lines = []
        list_type = None

        for line in lines:
            stripped = line.strip()

            # Check for list items
            if re.match(r"^[-*+]\s+", stripped):  # Unordered list
                if not in_list or list_type != "unordered":
                    if in_list:
                        # End previous list
                        lists.append(
                            {
                                "content": "\n".join(list_lines),
                                "type": list_type,
                                "items": len(list_lines),
                            }
                        )
                    in_list = True
                    list_type = "unordered"
                    list_lines = []
                list_lines.append(line)
            elif re.match(r"^\d+\.\s+", stripped):  # Ordered list
                if not in_list or list_type != "ordered":
                    if in_list:
                        # End previous list
                        lists.append(
                            {
                                "content": "\n".join(list_lines),
                                "type": list_type,
                                "items": len(list_lines),
                            }
                        )
                    in_list = True
                    list_type = "ordered"
                    list_lines = []
                list_lines.append(line)
            elif in_list and line.startswith("  "):  # Continuation of list item
                list_lines.append(line)
            else:
                if in_list:
                    # End of list
                    lists.append(
                        {
                            "content": "\n".join(list_lines),
                            "type": list_type,
                            "items": len(
                                [
                                    line
                                    for line in list_lines
                                    if re.match(r"^[-*+\d+\.]\s+", line.strip())
                                ]
                            ),
                        }
                    )
                    in_list = False

        # Handle list at end of content
        if in_list:
            lists.append(
                {
                    "content": "\n".join(list_lines),
                    "type": list_type,
                    "items": len(
                        [line for line in list_lines if re.match(r"^[-*+\d+\.]\s+", line.strip())]
                    ),
                }
            )

        return lists

    def _create_single_chunk(
        self, content: str, file_path: Path, file_type: str = "markdown"
    ) -> List[Dict[str, Any]]:
        """Create a single chunk for the entire file."""
        lines = content.split("\n")

        # Extract code blocks and links for metadata
        code_blocks = self._extract_code_blocks(content)
        links = self._extract_links(content)
        tables = self._extract_tables(content)
        lists = self._extract_lists(content)

        chunk = {
            "content": content,
            "metadata": {
                "chunk_type": "documentation_file",
                "start_line": 1,
                "end_line": len(lines),
                "code_blocks": code_blocks,
                "links": links,
                "tables": tables,
                "lists": lists,
                "language": file_type,
                "file_path": str(file_path),
                "total_lines": len(lines),
            },
        }

        return [chunk]

    def _fallback_parse(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Simple fallback parsing when normal parsing fails."""
        return self._create_single_chunk(content, file_path)
