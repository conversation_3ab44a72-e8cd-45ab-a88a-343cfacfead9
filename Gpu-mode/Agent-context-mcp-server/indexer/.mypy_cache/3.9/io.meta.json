{"data_mtime": 1752758957, "dep_lines": [1, 2, 3, 23, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 30, 30], "dependencies": ["abc", "sys", "_io", "typing", "builtins", "_frozen_importlib", "_typeshed"], "hash": "e2044203d69ac4d4194d2057e9ddcfa16e4d3d6e", "id": "io", "ignore_all": true, "interface_hash": "5ec4b24f14adacacc26822ac4fb56b90a469ccb4", "mtime": 1752722366, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/Code/Gpu-mode/Agent-context-mcp-server/indexer/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/io.pyi", "plugin_data": null, "size": 1898, "suppressed": [], "version_id": "1.17.0"}