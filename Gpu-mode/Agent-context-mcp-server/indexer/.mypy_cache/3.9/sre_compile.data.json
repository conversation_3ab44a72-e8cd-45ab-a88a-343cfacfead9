{".class": "MypyFile", "_fullname": "sre_compile", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ANY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ANY", "kind": "Gdef"}, "ANY_ALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ANY_ALL", "kind": "Gdef"}, "ASSERT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ASSERT", "kind": "Gdef"}, "ASSERT_NOT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ASSERT_NOT", "kind": "Gdef"}, "AT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT", "kind": "Gdef"}, "ATCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ATCODES", "kind": "Gdef"}, "AT_BEGINNING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING", "kind": "Gdef"}, "AT_BEGINNING_LINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING_LINE", "kind": "Gdef"}, "AT_BEGINNING_STRING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING_STRING", "kind": "Gdef"}, "AT_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BOUNDARY", "kind": "Gdef"}, "AT_END": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END", "kind": "Gdef"}, "AT_END_LINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END_LINE", "kind": "Gdef"}, "AT_END_STRING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END_STRING", "kind": "Gdef"}, "AT_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOCALE", "kind": "Gdef"}, "AT_LOC_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOC_BOUNDARY", "kind": "Gdef"}, "AT_LOC_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOC_NON_BOUNDARY", "kind": "Gdef"}, "AT_MULTILINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_MULTILINE", "kind": "Gdef"}, "AT_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_NON_BOUNDARY", "kind": "Gdef"}, "AT_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNICODE", "kind": "Gdef"}, "AT_UNI_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNI_BOUNDARY", "kind": "Gdef"}, "AT_UNI_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNI_NON_BOUNDARY", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BIGCHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.BIGCHARSET", "kind": "Gdef"}, "BRANCH": {".class": "SymbolTableNode", "cross_ref": "sre_constants.BRANCH", "kind": "Gdef"}, "CALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CALL", "kind": "Gdef"}, "CATEGORY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY", "kind": "Gdef"}, "CATEGORY_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_DIGIT", "kind": "Gdef"}, "CATEGORY_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LINEBREAK", "kind": "Gdef"}, "CATEGORY_LOC_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LOC_NOT_WORD", "kind": "Gdef"}, "CATEGORY_LOC_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LOC_WORD", "kind": "Gdef"}, "CATEGORY_NOT_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_DIGIT", "kind": "Gdef"}, "CATEGORY_NOT_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_LINEBREAK", "kind": "Gdef"}, "CATEGORY_NOT_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_SPACE", "kind": "Gdef"}, "CATEGORY_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_WORD", "kind": "Gdef"}, "CATEGORY_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_DIGIT", "kind": "Gdef"}, "CATEGORY_UNI_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_LINEBREAK", "kind": "Gdef"}, "CATEGORY_UNI_NOT_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_DIGIT", "kind": "Gdef"}, "CATEGORY_UNI_NOT_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_LINEBREAK", "kind": "Gdef"}, "CATEGORY_UNI_NOT_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_WORD", "kind": "Gdef"}, "CATEGORY_UNI_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_WORD", "kind": "Gdef"}, "CATEGORY_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_WORD", "kind": "Gdef"}, "CHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CHARSET", "kind": "Gdef"}, "CHCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CHCODES", "kind": "Gdef"}, "CH_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CH_LOCALE", "kind": "Gdef"}, "CH_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CH_UNICODE", "kind": "Gdef"}, "FAILURE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.FAILURE", "kind": "Gdef"}, "GROUPREF": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF", "kind": "Gdef"}, "GROUPREF_EXISTS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_EXISTS", "kind": "Gdef"}, "GROUPREF_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_IGNORE", "kind": "Gdef"}, "GROUPREF_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_LOC_IGNORE", "kind": "Gdef"}, "GROUPREF_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_UNI_IGNORE", "kind": "Gdef"}, "IN": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN", "kind": "Gdef"}, "INFO": {".class": "SymbolTableNode", "cross_ref": "sre_constants.INFO", "kind": "Gdef"}, "IN_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_IGNORE", "kind": "Gdef"}, "IN_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_LOC_IGNORE", "kind": "Gdef"}, "IN_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_UNI_IGNORE", "kind": "Gdef"}, "JUMP": {".class": "SymbolTableNode", "cross_ref": "sre_constants.JUMP", "kind": "Gdef"}, "LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL", "kind": "Gdef"}, "LITERAL_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_IGNORE", "kind": "Gdef"}, "LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_LOC_IGNORE", "kind": "Gdef"}, "LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_UNI_IGNORE", "kind": "Gdef"}, "MAGIC": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAGIC", "kind": "Gdef"}, "MARK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MARK", "kind": "Gdef"}, "MAXCODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.MAXCODE", "name": "MAXCODE", "setter_type": null, "type": "builtins.int"}}, "MAXGROUPS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAXGROUPS", "kind": "Gdef"}, "MAXREPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAXREPEAT", "kind": "Gdef"}, "MAX_REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAX_REPEAT", "kind": "Gdef"}, "MAX_UNTIL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAX_UNTIL", "kind": "Gdef"}, "MIN_REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_REPEAT", "kind": "Gdef"}, "MIN_REPEAT_ONE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_REPEAT_ONE", "kind": "Gdef"}, "MIN_UNTIL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_UNTIL", "kind": "Gdef"}, "NEGATE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NEGATE", "kind": "Gdef"}, "NOT_LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL", "kind": "Gdef"}, "NOT_LITERAL_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_IGNORE", "kind": "Gdef"}, "NOT_LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_LOC_IGNORE", "kind": "Gdef"}, "NOT_LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_UNI_IGNORE", "kind": "Gdef"}, "OPCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OPCODES", "kind": "Gdef"}, "OP_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_IGNORE", "kind": "Gdef"}, "OP_LOCALE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_LOCALE_IGNORE", "kind": "Gdef"}, "OP_UNICODE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_UNICODE_IGNORE", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RANGE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.RANGE", "kind": "Gdef"}, "RANGE_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.RANGE_UNI_IGNORE", "kind": "Gdef"}, "REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.REPEAT", "kind": "Gdef"}, "REPEAT_ONE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.REPEAT_ONE", "kind": "Gdef"}, "SRE_FLAG_ASCII": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_ASCII", "kind": "Gdef"}, "SRE_FLAG_DEBUG": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_DEBUG", "kind": "Gdef"}, "SRE_FLAG_DOTALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_DOTALL", "kind": "Gdef"}, "SRE_FLAG_IGNORECASE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_IGNORECASE", "kind": "Gdef"}, "SRE_FLAG_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_LOCALE", "kind": "Gdef"}, "SRE_FLAG_MULTILINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_MULTILINE", "kind": "Gdef"}, "SRE_FLAG_TEMPLATE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_TEMPLATE", "kind": "Gdef"}, "SRE_FLAG_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_UNICODE", "kind": "Gdef"}, "SRE_FLAG_VERBOSE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_VERBOSE", "kind": "Gdef"}, "SRE_INFO_CHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_CHARSET", "kind": "Gdef"}, "SRE_INFO_LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_LITERAL", "kind": "Gdef"}, "SRE_INFO_PREFIX": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_PREFIX", "kind": "Gdef"}, "SUBPATTERN": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SUBPATTERN", "kind": "Gdef"}, "SUCCESS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SUCCESS", "kind": "Gdef"}, "SubPattern": {".class": "SymbolTableNode", "cross_ref": "sre_parse.SubPattern", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NamedIntConstant": {".class": "SymbolTableNode", "cross_ref": "sre_constants._NamedIntConstant", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_compile.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["p", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_compile.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["p", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "sre_parse.SubPattern"], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_compile.dis", "name": "dis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["code"], "arg_types": [{".class": "Instance", "args": ["sre_constants._NamedIntConstant"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error": {".class": "SymbolTableNode", "cross_ref": "re.error", "kind": "Gdef"}, "isstring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_compile.isstring", "name": "is<PERSON>ring", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is<PERSON>ring", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/Code/Gpu-mode/Agent-context-mcp-server/indexer/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/sre_compile.pyi"}