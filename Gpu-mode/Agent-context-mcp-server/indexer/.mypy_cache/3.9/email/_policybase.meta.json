{"data_mtime": 1752758957, "dep_lines": [2, 3, 4, 1, 5, 6, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["email.errors", "email.header", "email.message", "abc", "typing", "typing_extensions", "builtins", "_frozen_importlib"], "hash": "f88f8001ceb5abe8238ded98d78e6fd4cbdd4e7a", "id": "email._policybase", "ignore_all": true, "interface_hash": "feb2ab25bf34d07b8f5f01b4fccdb46d495627f2", "mtime": 1752722366, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/Code/Gpu-mode/Agent-context-mcp-server/indexer/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/email/_policybase.pyi", "plugin_data": null, "size": 3331, "suppressed": [], "version_id": "1.17.0"}