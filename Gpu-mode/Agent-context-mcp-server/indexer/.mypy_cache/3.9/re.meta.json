{"data_mtime": 1752758957, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 8, 9, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "enum", "sre_compile", "sre_constants", "sys", "_typeshed", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "4f2dbd8c4e870c1639fe5c5d67b53b84e0af4a45", "id": "re", "ignore_all": true, "interface_hash": "7da72a8eb8de7018ed71a2c9eb4c4be57347363f", "mtime": 1752722366, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/Code/Gpu-mode/Agent-context-mcp-server/indexer/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/re.pyi", "plugin_data": null, "size": 12008, "suppressed": [], "version_id": "1.17.0"}