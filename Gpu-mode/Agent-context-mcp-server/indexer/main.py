"""
CLI Entry Point for RAG Indexer

This module provides the command-line interface for the RAG indexer,
allowing users to index codebases, update existing indices, and query
the indexed data for testing purposes.

Usage:
    python -m indexer index /path/to/repo
    python -m indexer index /path/to/repo --config custom_config.yaml
    python -m indexer update /path/to/repo --incremental
    python -m indexer query "how to authenticate users"
    python -m indexer stats
"""

import click
from pathlib import Path
from typing import Optional

from .config.settings import load_config
from .core.indexer import CodebaseIndexer
from .utils.logging import setup_logging


@click.group()
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
@click.option("--config", "-c", type=click.Path(exists=True), help="Configuration file path")
@click.pass_context
def cli(ctx: click.Context, verbose: bool, config: Optional[str]) -> None:
    """RAG Indexer CLI - Index codebases for semantic search."""
    ctx.ensure_object(dict)
    ctx.obj["verbose"] = verbose
    ctx.obj["config_path"] = config

    # Setup logging
    setup_logging(verbose=verbose)


@cli.command()
@click.argument("repository_path", type=click.Path(exists=True, path_type=Path))
@click.option("--output", "-o", type=click.Path(path_type=Path), help="Output directory for index")
@click.pass_context
def index(ctx: click.Context, repository_path: Path, output: Optional[Path]) -> None:
    """Index a repository for semantic search."""
    click.echo(f"Indexing repository: {repository_path}")

    # Load configuration
    config_path = ctx.obj.get("config_path")
    config = load_config(config_path, repository_path=repository_path, output_path=output)

    # Create and run indexer
    indexer = CodebaseIndexer(config)
    indexer.index_repository()

    click.echo("Indexing complete!")


@cli.command()
@click.argument("repository_path", type=click.Path(exists=True, path_type=Path))
@click.option("--incremental", is_flag=True, help="Perform incremental update")
@click.pass_context
def update(ctx: click.Context, repository_path: Path, incremental: bool) -> None:
    """Update an existing index."""
    click.echo(f"Updating index for repository: {repository_path}")
    # TODO: Implement update functionality
    click.echo("Update functionality not yet implemented")


@cli.command()
@click.argument("query", type=str)
@click.option("--limit", "-l", default=10, help="Number of results to return")
@click.pass_context
def query(ctx: click.Context, query: str, limit: int) -> None:
    """Query the index for testing purposes."""
    click.echo(f"Querying: {query}")
    # TODO: Implement query functionality
    click.echo("Query functionality not yet implemented")


@cli.command()
@click.pass_context
def stats(ctx: click.Context) -> None:
    """Show index statistics."""
    click.echo("Index statistics:")
    # TODO: Implement stats functionality
    click.echo("Stats functionality not yet implemented")


if __name__ == "__main__":
    cli()
